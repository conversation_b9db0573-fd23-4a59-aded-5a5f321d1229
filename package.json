{"name": "degen<PERSON>s", "version": "0.1.2", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@web3-react/core": "^6.1.9", "@web3-react/injected-connector": "^6.0.7", "@web3-react/walletconnect-connector": "^6.2.13", "@web3-react/walletlink-connector": "^6.2.14", "axios": "^1.4.0", "big-integer": "^1.6.51", "buffer": "^6.0.3", "emailjs-com": "^3.2.0", "ethers": "^5.7.2", "crypto": "^1.0.1", "fs": "^0.0.1-security", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-dom": "^18.2.0", "react-notifications": "^1.7.4", "react-player": "^2.12.0", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "request": "^2.88.2", "swiper": "^9.3.2", "sqlite3": "^5.1.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "npm run build & npm run dev", "build": "react-scripts build", "dev": "react-scripts start", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1"}}