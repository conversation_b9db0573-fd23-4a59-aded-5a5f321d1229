/* Global CSS fixes to ensure buttons are always visible */

/* Override any global hideimg classes - IMPORTANT: Must be more specific */
.hideimg,
.btn-badge.hideimg,
.wallet-connect-container .hideimg,
.wallet-connect-container .btn-badge {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure all buttons are visible on all screen sizes */
.btn, button, .wallet-main-btn {
  display: inline-flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force visibility for button components */
.btn-badge, .btn-badge img {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Wallet specific fixes */
.wallet-connect-container {
  display: block !important;
  visibility: visible !important;
}

.wallet-connect-container button,
.wallet-connect-container .btn {
  display: inline-flex !important;
  visibility: visible !important;
}

/* Override ANY responsive hiding - cover all screen sizes */
@media only screen and (max-width: 1600px) {
  .hideimg, .btn-badge.hideimg {
    display: inline-block !important;
  }
  .btn, button {
    display: inline-flex !important;
  }
}

@media only screen and (max-width: 1400px) {
  .hideimg, .btn-badge.hideimg {
    display: inline-block !important;
  }
  .btn, button {
    display: inline-flex !important;
  }
}

@media only screen and (max-width: 1200px) {
  .hideimg, .btn-badge.hideimg {
    display: inline-block !important;
  }
  .btn, button {
    display: inline-flex !important;
  }
}

@media only screen and (max-width: 991px) {
  .hideimg, .btn-badge.hideimg {
    display: inline-block !important;
  }
  .btn, button {
    display: inline-flex !important;
  }
}

@media only screen and (min-width: 992px) {
  .hideimg, .btn-badge.hideimg {
    display: inline-block !important;
  }
  .btn, button {
    display: inline-flex !important;
  }
}
