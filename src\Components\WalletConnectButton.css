/* WalletConnectButton Styles */
.wallet-connect-container {
  position: relative;
}

.wallet-main-btn {
  background: linear-gradient(45deg, #F5C700, #FFD700);
  border: 2px solid #F5C700;
  border-radius: 12px;
  color: #000 !important;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(245, 199, 0, 0.3);
}

.wallet-main-btn:hover {
  background: linear-gradient(45deg, #FFD700, #F5C700);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 199, 0, 0.4);
}

.wallet-main-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.wallet-options-dropdown,
.wallet-info-dropdown {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wallet-option {
  transition: all 0.3s ease !important;
}

.wallet-option:hover {
  transform: translateX(5px) !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  .wallet-connect-container {
    width: 100%;
  }
  
  .wallet-main-btn {
    min-width: 150px;
    font-size: 14px;
  }
  
  .wallet-options-dropdown,
  .wallet-info-dropdown {
    left: -20px;
    right: -20px;
  }
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.1em;
}

/* Notification styles override */
.notification-container {
  z-index: 9999;
}

.notification-container .notification {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.notification-container .notification.notification-success {
  background-color: #28a745;
  border-left: 4px solid #20c997;
}

.notification-container .notification.notification-error {
  background-color: #dc3545;
  border-left: 4px solid #e74c3c;
}

.notification-container .notification.notification-warning {
  background-color: #ffc107;
  border-left: 4px solid #f39c12;
  color: #000;
}

.notification-container .notification.notification-info {
  background-color: #17a2b8;
  border-left: 4px solid #3498db;
}

/* Override any global hideimg classes for wallet buttons */
.wallet-connect-container .btn-badge {
  display: inline-block !important;
}

.wallet-connect-container .btn-badge img {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure wallet button is always visible */
.wallet-main-btn {
  display: flex !important;
  visibility: visible !important;
}

/* Make sure all wallet-related buttons are visible on all screen sizes */
.wallet-connect-container button,
.wallet-connect-container .btn {
  display: inline-flex !important;
  visibility: visible !important;
}

/* CRITICAL: Override hideimg class specifically for wallet components */
.wallet-connect-container .hideimg,
.wallet-connect-container .btn-badge.hideimg {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force visibility on all screen sizes */
@media only screen and (min-width: 992px) {
  .wallet-connect-container .hideimg,
  .wallet-connect-container .btn-badge {
    display: inline-block !important;
  }
  
  .wallet-main-btn {
    display: flex !important;
  }
}

@media only screen and (max-width: 991px) {
  .wallet-connect-container .hideimg,
  .wallet-connect-container .btn-badge {
    display: inline-block !important;
  }
}
