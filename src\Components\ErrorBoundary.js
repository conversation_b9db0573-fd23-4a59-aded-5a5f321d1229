import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          backgroundColor: '#1e1e1e',
          border: '2px solid #dc3545',
          borderRadius: '15px',
          padding: '30px',
          margin: '20px',
          textAlign: 'center'
        }}>
          <h3 className="text-danger mb-3">⚠️ Something went wrong</h3>
          <p className="text-white mb-3">
            An error occurred in the wallet component. This is usually due to browser extension conflicts.
          </p>
          
          <div style={{
            backgroundColor: '#2d2d2d',
            padding: '15px',
            borderRadius: '8px',
            marginBottom: '20px',
            textAlign: 'left'
          }}>
            <h5 className="text-warning">Troubleshooting Steps:</h5>
            <ul className="text-white" style={{ fontSize: '14px' }}>
              <li>Refresh the page and try again</li>
              <li>Disable and re-enable MetaMask extension</li>
              <li>Try using a different wallet (WalletConnect or Coinbase)</li>
              <li>Clear browser cache and cookies</li>
              <li>Try in incognito/private browsing mode</li>
            </ul>
          </div>

          <button 
            className="btn btn-outline-warning"
            onClick={() => window.location.reload()}
          >
            🔄 Refresh Page
          </button>

          {process.env.NODE_ENV === 'development' && (
            <details style={{ marginTop: '20px', textAlign: 'left' }}>
              <summary className="text-warning">Developer Info</summary>
              <pre className="text-danger" style={{ fontSize: '12px', marginTop: '10px' }}>
                {this.state.error && this.state.error.toString()}
                <br />
                {this.state.errorInfo.componentStack}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
