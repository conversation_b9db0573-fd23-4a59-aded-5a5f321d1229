.App {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blue-button {
  appearance: button;
  background-color: #1899d6;
  border: solid transparent;
  border-radius: 16px;
  border-width: 0 0 4px;
  box-sizing: border-box;
  color: #fff;
  cursor: pointer;
  font-size: 15px;
  font-weight: 700;
  letter-spacing: 0.8px;
  line-height: 20px;
  margin: 0;
  padding: 20px 16px;
  text-align: center;
  text-transform: uppercase;
  transform: translateZ(0);
  user-select: none;
  -webkit-user-select: none;
  width: 200px;
}

.blue-button:after {
  background-clip: padding-box;
  background-color: #1cb0f6;
  border: solid transparent;
  border-radius: 16px;
  border-width: 0 0 5px;
  bottom: -4px;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}

.buttblue-buttonon:main,
.blue-button:focus {
  user-select: auto;
}

.blue-button:hover:not(:disabled) {
  filter: brightness(1.1);
  -webkit-filter: brightness(1.1);
}

.blue-button:active:after {
  background-color: #1899d6;
}

.custom_modal {
  position: relative;
  background-color: #fff;
  width: 500px;
  height: 200px;
  border: 1px solid rgb(179, 179, 179);
  border-radius: 15px;
  overflow: hidden;
  margin: 0 15px 160px;
}

.custom_modal .footer {
  width: 100%;
  height: 60px;
  background-color: rgb(243, 244, 246);
  position: absolute;
  bottom: 0;
  display: flex;
  padding: 10px 1.5rem;
  flex-direction: row-reverse;
}

.custom_modal-wrapper {
  display: flex;
  position: absolute;
  justify-content: center;
  align-items: center;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  background-color: rgba(107, 114, 128, 0.5);
  z-index: 9;
  width: 100%;
  min-height: 100vh;
}

.custom_modal button {
  border-radius: 0.375rem;
  border-style: none;
  background: rgb(220, 38, 38);
  padding: 10px;
  color: #fff;
  line-height: 1.25rem;
  cursor: pointer;
}

.custom_modal .body {
  padding: 20px;
}
