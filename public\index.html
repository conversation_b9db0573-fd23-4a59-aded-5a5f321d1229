<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.ico" /> -->
    <link rel="icon" href="%PUBLIC_URL%/assets/images/degen.png" />
    
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <!-- ====================== CSS CDN ======================== -->

    <!-- ====================== SWIPER SLIDER CDN ======================== -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <!-- ====================== SWIPER SLIDER CDN ======================== -->

    <!-- ====================== JQUERY CDN ======================== -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <!-- ====================== JQUERY CDN ======================== -->
    <link rel="shortcut icon" href="./assets/images/degen.png" type="image/x-icon"/>

    <!-- ====================== STYLE CSS CDN ======================== -->
    <link rel="stylesheet" href="./assets/css/styel.css">
    <!-- ====================== STYLE CDN ======================== -->

    <script src="./assets/js/action.js"></script>
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>DEGENBULLS</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous">
    </script>
    <!-- ====================== JS BOOTSTRAP CDN ======================== -->

    <!-- ====================== JS SWIPER SLIDER CDN ======================== -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <!-- ====================== JS SWIPER SLIDER CDN ======================== -->




    <script>
      document.onreadystatechange = function () {
        if (document.readyState === "complete") {
        const swiper = new Swiper('.swiper1', {
            // Optional parameters
            direction: 'horizontal',
            slidesPerView: 1,
            loop: true,
            navigation: {
                nextEl: '.next-button-1',
                prevEl: '.prev-button-1',
            },
        });

        const swiper2 = new Swiper('.swiper2', {
            direction: 'horizontal',
            slidesPerView: 1,
            loop: true,
            navigation: {
                nextEl: '.next-button-2',
                prevEl: '.prev-button-2',
            },
        });

        const swiper3 = new Swiper('.swiper3', {
            direction: 'horizontal',
            slidesPerView: 1,
            loop: true,
            navigation: {
                nextEl: '.next-button-3',
                prevEl: '.prev-button-3',
            },
        });
      }
    }
    </script>
    <!-- <script>
       $(document).ready(function(){
          var acc = document.getElementsByClassName("accordion");
          var i;

          for (i = 0; i < acc.length; i++) {
              acc[i].addEventListener("click", function() {
                  console.log('here');
                  this.classList.toggle("active");
                  var panel = this.nextElementSibling;
                  if (panel.style.display === "none") {
                      panel.style.display = "block";
                  } else {
                      panel.style.display = "none";
                  }
              });
          }
        });
    </script> -->
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
