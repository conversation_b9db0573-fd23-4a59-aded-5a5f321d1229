import React, { useState, useEffect } from "react";
import { useWeb3React } from "@web3-react/core";
import { injected, walletconnect, coinbasewallet } from "../connectors";
import { NotificationManager } from "react-notifications";
import { getEllipsisTxt } from "../utils";
import "./WalletConnectButton.css";

const WalletConnectButton = ({ className = "", style = {} }) => {
  const { account, activate, deactivate, error } = useWeb3React();
  const [isConnecting, setIsConnecting] = useState(false);
  const [showWalletOptions, setShowWalletOptions] = useState(false);

  // Check if MetaMask is installed
  const isMetamaskInstalled = () => {
    return typeof window.ethereum !== "undefined";
  };

  // Connect to MetaMask
  const connectMetaMask = async () => {
    setIsConnecting(true);
    try {
      if (!isMetamaskInstalled()) {
        NotificationManager.warning("Please install MetaMask extension", "MetaMask Required");
        setIsConnecting(false);
        return;
      }

      // Add timeout to prevent hanging on MetaMask issues
      const connectPromise = activate(injected);
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout - please try again')), 15000)
      );

      await Promise.race([connectPromise, timeoutPromise]);
      NotificationManager.success("MetaMask connected successfully!", "Success");
      setShowWalletOptions(false);
    } catch (err) {
      console.error("MetaMask connection error:", err);
      let errorMessage = "Failed to connect MetaMask";
      
      if (err.message?.includes('timeout')) {
        errorMessage = "Connection timeout - please try again";
      } else if (err.message?.includes('rejected')) {
        errorMessage = "Connection rejected by user";
      } else if (err.message?.includes('type')) {
        errorMessage = "MetaMask extension issue - please refresh and try again";
      }
      
      NotificationManager.error(errorMessage, "Connection Error", 5000);
    }
    setIsConnecting(false);
  };

  // Connect to WalletConnect
  const connectWalletConnect = async () => {
    setIsConnecting(true);
    try {
      // Reset WalletConnect before connecting
      if (walletconnect?.connector) {
        await walletconnect.connector.killSession();
      }
      
      const connectPromise = activate(walletconnect);
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('WalletConnect timeout')), 30000)
      );

      await Promise.race([connectPromise, timeoutPromise]);
      NotificationManager.success("WalletConnect connected successfully!", "Success");
      setShowWalletOptions(false);
    } catch (err) {
      console.error("WalletConnect connection error:", err);
      let errorMessage = "Failed to connect WalletConnect";
      
      if (err.message?.includes('timeout')) {
        errorMessage = "Connection timeout - please scan QR code faster";
      } else if (err.message?.includes('rejected')) {
        errorMessage = "Connection rejected in wallet app";
      }
      
      NotificationManager.error(errorMessage, "WalletConnect Error", 5000);
    }
    setIsConnecting(false);
  };

  // Connect to Coinbase Wallet
  const connectCoinbase = async () => {
    setIsConnecting(true);
    try {
      const connectPromise = activate(coinbasewallet);
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Coinbase connection timeout')), 20000)
      );

      await Promise.race([connectPromise, timeoutPromise]);
      NotificationManager.success("Coinbase Wallet connected successfully!", "Success");
      setShowWalletOptions(false);
    } catch (err) {
      console.error("Coinbase connection error:", err);
      let errorMessage = "Failed to connect Coinbase Wallet";
      
      if (err.message?.includes('timeout')) {
        errorMessage = "Connection timeout - please try again";
      } else if (err.message?.includes('rejected')) {
        errorMessage = "Connection rejected by user";
      }
      
      NotificationManager.error(errorMessage, "Coinbase Error", 5000);
    }
    setIsConnecting(false);
  };

  // Disconnect wallet
  const disconnectWallet = async () => {
    try {
      deactivate();
      NotificationManager.info("Wallet disconnected", "Info");
    } catch (err) {
      console.error("Disconnect error:", err);
    }
  };

  // Handle wallet button click
  const handleWalletClick = () => {
    if (account) {
      disconnectWallet();
    } else {
      setShowWalletOptions(!showWalletOptions);
    }
  };

  // Close wallet options when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.wallet-connect-container')) {
        setShowWalletOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`wallet-connect-container ${className}`} style={{ 
      position: 'relative', 
      display: 'block !important',
      visibility: 'visible !important',
      opacity: '1 !important',
      ...style 
    }}>
      {/* Main Wallet Button */}
      <button
        className="btn sign__btn text-black wallet-main-btn"
        onClick={handleWalletClick}
        disabled={isConnecting}
        style={{
          minWidth: "200px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          gap: "8px",
          ...style
        }}
      >
        <div className="card__boxbolt" style={{ marginLeft: "10px" }}>
          {account ? (
            <>
              <p className="oneline" style={{ marginBottom: "0px" }}>
                {getEllipsisTxt(account, 6)}
              </p>
              <span className="btn-badge" style={{ 
                marginLeft: "5px", 
                display: "inline-block !important",
                visibility: "visible !important",
                opacity: "1 !important"
              }}>
                <img src="/assets/images/arow-top.png" alt="arrow" style={{
                  display: "inline-block !important",
                  visibility: "visible !important",
                  opacity: "1 !important"
                }} />
              </span>
            </>
          ) : (
            <>
              <p className="oneline" style={{ marginBottom: "0px" }}>
                {isConnecting ? "Connecting..." : "Connect Wallet"}
              </p>
              <span className="btn-badge" style={{ 
                marginLeft: "5px", 
                display: "inline-block !important",
                visibility: "visible !important",
                opacity: "1 !important"
              }}>
                <img src="/assets/images/arow-top.png" alt="arrow" style={{
                  display: "inline-block !important",
                  visibility: "visible !important",
                  opacity: "1 !important"
                }} />
              </span>
            </>
          )}
        </div>
      </button>

      {/* Wallet Options Dropdown */}
      {showWalletOptions && !account && (
        <div
          className="wallet-options-dropdown"
          style={{
            position: "absolute",
            top: "100%",
            left: "0",
            right: "0",
            backgroundColor: "#1e1e1e",
            border: "2px solid #F5C700",
            borderRadius: "15px",
            padding: "20px",
            zIndex: 1000,
            marginTop: "10px",
            boxShadow: "0 10px 30px rgba(0,0,0,0.3)"
          }}
        >
          <h5 className="text-yellow text-center mb-3">Choose Wallet</h5>
          
          {/* MetaMask Option */}
          <div
            className="wallet-option"
            onClick={connectMetaMask}
            style={{
              display: "flex",
              alignItems: "center",
              padding: "15px",
              marginBottom: "10px",
              backgroundColor: "#2d2d2d",
              borderRadius: "10px",
              cursor: "pointer",
              border: "1px solid transparent",
              transition: "all 0.3s ease"
            }}
            onMouseEnter={(e) => {
              e.target.style.borderColor = "#F5C700";
              e.target.style.backgroundColor = "#3d3d3d";
            }}
            onMouseLeave={(e) => {
              e.target.style.borderColor = "transparent";
              e.target.style.backgroundColor = "#2d2d2d";
            }}
          >
            <img 
              src="/assets/images/metamask.png" 
              alt="MetaMask" 
              style={{ width: "30px", height: "30px", marginRight: "15px" }}
            />
            <div>
              <p className="text-white mb-0" style={{ fontWeight: "bold" }}>MetaMask</p>
              <small className="text-yellow">
                {isMetamaskInstalled() ? "Connect using browser wallet" : "Install MetaMask"}
              </small>
            </div>
          </div>

          {/* WalletConnect Option */}
          <div
            className="wallet-option"
            onClick={connectWalletConnect}
            style={{
              display: "flex",
              alignItems: "center",
              padding: "15px",
              marginBottom: "10px",
              backgroundColor: "#2d2d2d",
              borderRadius: "10px",
              cursor: "pointer",
              border: "1px solid transparent",
              transition: "all 0.3s ease"
            }}
            onMouseEnter={(e) => {
              e.target.style.borderColor = "#F5C700";
              e.target.style.backgroundColor = "#3d3d3d";
            }}
            onMouseLeave={(e) => {
              e.target.style.borderColor = "transparent";
              e.target.style.backgroundColor = "#2d2d2d";
            }}
          >
            <img 
              src="/assets/images/walletconnect.png" 
              alt="WalletConnect" 
              style={{ width: "30px", height: "30px", marginRight: "15px" }}
            />
            <div>
              <p className="text-white mb-0" style={{ fontWeight: "bold" }}>WalletConnect</p>
              <small className="text-yellow">Scan with mobile wallet</small>
            </div>
          </div>

          {/* Coinbase Wallet Option */}
          <div
            className="wallet-option"
            onClick={connectCoinbase}
            style={{
              display: "flex",
              alignItems: "center",
              padding: "15px",
              backgroundColor: "#2d2d2d",
              borderRadius: "10px",
              cursor: "pointer",
              border: "1px solid transparent",
              transition: "all 0.3s ease"
            }}
            onMouseEnter={(e) => {
              e.target.style.borderColor = "#F5C700";
              e.target.style.backgroundColor = "#3d3d3d";
            }}
            onMouseLeave={(e) => {
              e.target.style.borderColor = "transparent";
              e.target.style.backgroundColor = "#2d2d2d";
            }}
          >
            <img 
              src="/assets/images/coinbase.png" 
              alt="Coinbase Wallet" 
              style={{ width: "30px", height: "30px", marginRight: "15px" }}
            />
            <div>
              <p className="text-white mb-0" style={{ fontWeight: "bold" }}>Coinbase Wallet</p>
              <small className="text-yellow">Connect with Coinbase</small>
            </div>
          </div>
        </div>
      )}

      {/* Connected Wallet Info */}
      {account && showWalletOptions && (
        <div
          className="wallet-info-dropdown"
          style={{
            position: "absolute",
            top: "100%",
            left: "0",
            right: "0",
            backgroundColor: "#1e1e1e",
            border: "2px solid #F5C700",
            borderRadius: "15px",
            padding: "20px",
            zIndex: 1000,
            marginTop: "10px",
            boxShadow: "0 10px 30px rgba(0,0,0,0.3)"
          }}
        >
          <h5 className="text-yellow text-center mb-3">Wallet Connected</h5>
          <div className="text-center">
            <p className="text-white mb-2">
              <strong>Address:</strong> {getEllipsisTxt(account, 8)}
            </p>
            <button
              className="btn btn-outline-danger btn-sm"
              onClick={disconnectWallet}
              style={{ marginTop: "10px" }}
            >
              Disconnect Wallet
            </button>
          </div>
        </div>
      )}

      {/* Loading Overlay */}
      {isConnecting && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0,0,0,0.7)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: "8px",
            zIndex: 1001
          }}
        >
          <div className="text-yellow">
            <div className="spinner-border spinner-border-sm me-2" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            Connecting...
          </div>
        </div>
      )}
    </div>
  );
};

export default WalletConnectButton;
