import React, { useState, useEffect } from 'react';
import { useWeb3React } from "@web3-react/core";
import WalletConnectButton from '../Components/WalletConnectButton';
import ErrorBoundary from '../Components/ErrorBoundary';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import 'react-notifications/lib/notifications.css';
import '../fix-buttons.css';

// Add inline styles to ensure buttons are always visible
const ensureVisibleStyle = {
  display: 'inline-block !important',
  visibility: 'visible !important',
  opacity: '1 !important'
};

const buttonStyle = {
  ...ensureVisibleStyle,
  minWidth: '150px',
  padding: '10px 20px'
};

function WalletDemo() {
  const { account, library, chainId, error } = useWeb3React();
  const [balance, setBalance] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Reset balance when account changes
  useEffect(() => {
    if (!account) {
      setBalance(null);
    }
  }, [account]);

  // Handle connection errors
  useEffect(() => {
    if (error) {
      console.error('Web3 Error:', error);
      NotificationManager.error(
        'Wallet connection error. Please try again.',
        'Connection Error',
        5000
      );
    }
  }, [error]);

  // Get wallet balance with proper error handling
  const getBalance = async () => {
    if (!account || !library) {
      NotificationManager.warning('Please connect your wallet first', 'Wallet Required');
      return;
    }

    setIsLoading(true);
    try {
      // Add timeout to prevent hanging
      const balancePromise = library.getBalance(account);
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), 10000)
      );

      const balanceResult = await Promise.race([balancePromise, timeoutPromise]);
      const balanceInEth = parseFloat(balanceResult.toString()) / Math.pow(10, 18);
      
      setBalance(balanceInEth);
      NotificationManager.success('Balance retrieved successfully!', 'Success');
    } catch (error) {
      console.error('Error getting balance:', error);
      NotificationManager.error(
        `Failed to get balance: ${error.message || 'Unknown error'}`,
        'Balance Error',
        5000
      );
      setBalance(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Check network with fallback
  const getNetworkName = (chainId) => {
    try {
      const networks = {
        1: 'Ethereum Mainnet',
        5: 'Goerli Testnet',
        56: 'BSC Mainnet',
        97: 'BSC Testnet',
        137: 'Polygon Mainnet',
        80001: 'Mumbai Testnet',
        11155111: 'Sepolia Testnet',
        42161: 'Arbitrum One',
        10: 'Optimism',
        43114: 'Avalanche C-Chain'
      };
      return networks[chainId] || `Unknown Network (Chain ID: ${chainId})`;
    } catch (error) {
      console.error('Error getting network name:', error);
      return 'Network Unknown';
    }
  };

  // Safe address display
  const getDisplayAddress = (address) => {
    try {
      if (!address || typeof address !== 'string') return 'Invalid Address';
      return address;
    } catch (error) {
      console.error('Error displaying address:', error);
      return 'Address Error';
    }
  };

  return (
    <ErrorBoundary>
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%)',
        padding: '20px'
      }}>
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              {/* Header */}
              <div className="text-center mb-5">
                <h1 className="text-yellow mb-3" style={{ fontSize: '3rem', fontWeight: 'bold' }}>
                  🚀 DEGENBULLS Wallet Demo
                </h1>
                <p className="text-white" style={{ fontSize: '1.2rem' }}>
                  Test our advanced wallet integration with multiple providers
                </p>
              </div>

              {/* Main Card */}
              <div style={{
                backgroundColor: '#1e1e1e',
                border: '2px solid #F5C700',
                borderRadius: '20px',
                padding: '40px',
                boxShadow: '0 10px 30px rgba(245, 199, 0, 0.2)'
              }}>
                {/* Wallet Connect Section */}
                <div className="text-center mb-4">
                  <h3 className="text-yellow mb-3">Connect Your Wallet</h3>
                  <ErrorBoundary>
                    <WalletConnectButton style={{ 
                      width: '100%', 
                      maxWidth: '400px', 
                      margin: '0 auto',
                      ...buttonStyle
                    }} />
                  </ErrorBoundary>
                </div>

              {/* Connection Status */}
              {account ? (
                <div style={{
                  backgroundColor: '#2d2d2d',
                  borderRadius: '15px',
                  padding: '30px',
                  marginTop: '30px',
                  border: '1px solid #28a745'
                }}>
                  <h4 className="text-success mb-3">✅ Wallet Connected</h4>
                  
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="text-yellow">Address:</label>
                        <p className="text-white" style={{ 
                          backgroundColor: '#1e1e1e', 
                          padding: '10px', 
                          borderRadius: '8px',
                          fontFamily: 'monospace',
                          fontSize: '14px',
                          wordBreak: 'break-all'
                        }}>
                          {getDisplayAddress(account)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="text-yellow">Network:</label>
                        <p className="text-white" style={{ 
                          backgroundColor: '#1e1e1e', 
                          padding: '10px', 
                          borderRadius: '8px' 
                        }}>
                          {chainId ? getNetworkName(chainId) : 'Unknown'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="text-center mt-3">
                    <button 
                      className="btn btn-outline-warning"
                      onClick={getBalance}
                      disabled={isLoading || !account || !library}
                      style={{ marginRight: '10px', ...buttonStyle }}
                    >
                      {isLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status">
                            <span className="visually-hidden">Loading...</span>
                          </span>
                          Getting Balance...
                        </>
                      ) : (
                        'Get Balance'
                      )}
                    </button>
                    
                    {balance !== null && !isLoading && (
                      <div className="mt-3">
                        <span className="text-yellow">Balance: </span>
                        <span className="text-white">{balance.toFixed(6)} ETH</span>
                      </div>
                    )}

                    {error && (
                      <div className="mt-3 alert alert-danger" style={{ fontSize: '14px' }}>
                        <strong>Connection Error:</strong> {error.message || 'Unknown error occurred'}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div style={{
                  backgroundColor: '#2d2d2d',
                  borderRadius: '15px',
                  padding: '30px',
                  marginTop: '30px',
                  border: '1px solid #ffc107',
                  textAlign: 'center'
                }}>
                  <h4 className="text-warning mb-3">⚠️ No Wallet Connected</h4>
                  <p className="text-white">
                    Connect your wallet to access DEGENBULLS features and view your mining NFTs.
                  </p>
                </div>
              )}

              {/* Features List */}
              <div style={{
                backgroundColor: '#2d2d2d',
                borderRadius: '15px',
                padding: '30px',
                marginTop: '30px'
              }}>
                <h4 className="text-yellow mb-3">🔧 Integration Features</h4>
                <div className="row">
                  <div className="col-md-6">
                    <ul className="text-white" style={{ listStyle: 'none', padding: 0 }}>
                      <li className="mb-2">✅ MetaMask Integration</li>
                      <li className="mb-2">✅ WalletConnect Protocol</li>
                      <li className="mb-2">✅ Coinbase Wallet Support</li>
                      <li className="mb-2">✅ Multi-chain Compatible</li>
                    </ul>
                  </div>
                  <div className="col-md-6">
                    <ul className="text-white" style={{ listStyle: 'none', padding: 0 }}>
                      <li className="mb-2">✅ Real-time Balance Display</li>
                      <li className="mb-2">✅ Network Detection</li>
                      <li className="mb-2">✅ Connection State Management</li>
                      <li className="mb-2">✅ Error Handling</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <div className="text-center mt-4">
                <a href="/" className="btn btn-outline-warning me-3" style={buttonStyle}>
                  ← Back to Home
                </a>
                <a href="/wallet-test" className="btn btn-outline-info" style={buttonStyle}>
                  Simple Test Page
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <NotificationContainer />
    </div>
    </ErrorBoundary>
  );
}

export default WalletDemo;
