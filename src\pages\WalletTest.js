import React from 'react';
import WalletConnectButton from '../Components/WalletConnectButton';
import { NotificationContainer } from 'react-notifications';
import 'react-notifications/lib/notifications.css';

function WalletTest() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#1e1e1e',
      padding: '50px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        backgroundColor: '#2d2d2d',
        padding: '40px',
        borderRadius: '20px',
        border: '2px solid #F5C700',
        maxWidth: '500px',
        width: '100%',
        textAlign: 'center'
      }}>
        <h1 className="text-yellow mb-4">Wallet Connect Test</h1>
        <p className="text-white mb-4">
          Test the new WalletConnectButton component with MetaMask, WalletConnect, and Coinbase Wallet integration.
        </p>
        
        {/* Our new WalletConnectButton */}
        <WalletConnectButton 
          className="mb-4"
          style={{ 
            width: '100%',
            marginBottom: '20px'
          }}
        />

        <div className="mt-4">
          <h5 className="text-yellow">Features:</h5>
          <ul className="text-white" style={{ textAlign: 'left', listStyle: 'none', padding: 0 }}>
            <li>✅ MetaMask Integration</li>
            <li>✅ WalletConnect Support</li>
            <li>✅ Coinbase Wallet Support</li>
            <li>✅ Connection Status Display</li>
            <li>✅ Disconnect Functionality</li>
            <li>✅ Loading States</li>
            <li>✅ Error Handling</li>
            <li>✅ Responsive Design</li>
          </ul>
        </div>

        <div className="mt-4">
          <a href="/" className="btn btn-outline-warning">
            ← Back to Home
          </a>
        </div>
      </div>

      {/* Notification Container for success/error messages */}
      <NotificationContainer />
    </div>
  );
}

export default WalletTest;
